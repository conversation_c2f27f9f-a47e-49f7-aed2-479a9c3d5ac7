import React from 'react';
import AssemblyFormWrapper from '@/app/components/forms/AssemblyFormWrapper';
import AssemblyFormContent from './AssemblyFormContent';

/**
 * Interface for route parameters including the assembly ID
 */
interface EditAssemblyPageProps {
  // In Next.js App Router, params is a Promise that needs to be awaited
  params: Promise<{
    id: string;
  }>;
}

/**
 * Edit Assembly page component - allows editing an existing assembly
 */
export default async function EditAssemblyPage({ params }: EditAssemblyPageProps) {
  // Await params to fix Next.js App Router issue
  const resolvedParams = await params;
  console.log("[EditAssemblyPage] Received params:", JSON.stringify(resolvedParams));

  const assemblyId = resolvedParams?.id;
  console.log("[EditAssemblyPage] Extracted assemblyId type: " + typeof assemblyId + ", value: " + String(assemblyId));

  if (!assemblyId) {
    console.error("[EditAssemblyPage] assemblyId is missing or invalid.");
    // Optionally, render an error message or redirect
    return <div>Error: Assembly ID is missing.</div>;
  }

  return (
    <AssemblyFormWrapper assemblyId={assemblyId}>
      <AssemblyFormContent />
    </AssemblyFormWrapper>
  );
}